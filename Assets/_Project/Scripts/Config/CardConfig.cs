public struct CardConfig
{
    public int VisualPositionID;
    
    public CardSuit Suit;
    public CardRank Rank;

    public int[] BlockingOtherCards;
    public int[] BlockedByCards;
    
    public CardConfig(int visualPositionID, string cardString, int[] blockingOtherCards, int[] blockedByCards)
    {
        // First character is the suit
        char suitChar = cardString[0];
        Suit = (CardSuit)suitChar;
        
        // Remaining characters are the rank
        string rankPart = cardString.Substring(1);
        Rank = (CardRank)int.Parse(rankPart);
        
        VisualPositionID = visualPositionID;
        BlockingOtherCards = blockingOtherCards;
        BlockedByCards = blockedByCards;
    }
}
