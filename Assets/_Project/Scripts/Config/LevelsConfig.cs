using System.Collections.Generic;
using UnityEngine;

public class LevelConfig
{
    public List<CardConfig> Cards;
    public Stack<CardConfig> Deck;
}

public class LevelsConfig
{
    public LevelConfig[] Levels;
    public void GenerateTestingData()
    {
        Levels = new LevelConfig[1];
        Levels[0] = new LevelConfig()
        {
            Cards = Consts.DEBUGGenerateLevel1Puzzle(),
            Deck = Consts.DEBUGGenerateLevel1Deck()
        };
    }
}
