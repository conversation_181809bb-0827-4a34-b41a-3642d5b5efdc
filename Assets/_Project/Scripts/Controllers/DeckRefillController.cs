using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public struct DeckRefilledEvent : IEvent
{
    public List<CardData> AddedCards;
    public DeckRefilledEvent(List<CardData> addedCards)
    {
        AddedCards = addedCards;
    }
}

public class DeckRefillController : BaseController
{
    public DeckRefillController()
    {
        Subscribe<DeckRefillOfferInteractedEvent>(OnDeckRefillOfferInteracted);
    }

    private void OnDeckRefillOfferInteracted(DeckRefillOfferInteractedEvent evt)
    {
        if (!evt.Accepted)
            return;

        List<CardData> addedCards = GenerateCardsToRefillDeck(Consts.DECK_REFILL_CARD_AMOUNT, Consts.DECK_REFILL_PLAYABLE_CARDS);
        Session.CurrentPuzzle.AddCardsToDeck(addedCards);
        
        Notify(new DeckRefilledEvent(addedCards));
    }

    private List<CardData> GenerateCardsToRefillDeck(int cardAmount, int playableCardAmount)
    {
        List<CardData> gc = new List<CardData>();
        
        //  Get a selected amount of cards at random from all possible playable cards
        List<CardData> playableCards = GeneratePossiblePlayableCards()
            .OrderBy(x => Random.value)
            .Take(playableCardAmount)
            .ToList();
        
        
        List<CardData> randomCards = GenerateRandomCards(cardAmount - playableCardAmount);
        
        Debug.Log($"DeckRefillController - Refilled deck with:\n" +
                  $"Playable cards: {string.Join(", ", playableCards.Select(x => x.ToString()))}, " +
                  $"random cards: {string.Join(", ", randomCards.Select(x => x.ToString()))}");
        
        gc.AddRange(playableCards);
        gc.AddRange(randomCards);
        
        return gc;
    }

    private List<CardData> GeneratePossiblePlayableCards()
    {
        var topCards = Session.CurrentPuzzle.TopPuzzleCards;
        HashSet<CardData> possibleCards = new HashSet<CardData>();
        
        foreach (var card in topCards)
        {
            string[] suits = { "h", "d", "c", "s" };
            foreach (var suit in suits)
            {
                // Add cards with same suit and adjacent ranks
                if ((int)card.Rank > 2)
                    possibleCards.Add(new CardData($"{suit}{(int)card.Rank - 1}"));
                if ((int)card.Rank < 14)
                    possibleCards.Add(new CardData($"{suit}{(int)card.Rank + 1}"));
            }
        }

        return possibleCards.ToList();
    }

    private List<CardData> GenerateRandomCards(int playableCardAmount)
    {
        List<CardData> gc = new List<CardData>();
        for (int i = 0; i < playableCardAmount; i++)
        {
            // TODO: This can clearly be more generic. General play rules should be addressed in a centralized place
            // and rules should be stackable with a generic way to define interactions between cards. 
            char suit = "hdcs"[Random.Range(0, 4)];
            int rank = Random.Range(2, 15);  // 2-14 (where 14 is Ace)
            gc.Add(new CardData($"{suit}{rank}"));
        }

        return gc;
    }
}
