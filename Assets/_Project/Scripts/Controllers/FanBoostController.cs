using System.Collections.Generic;
using UnityEngine;

public struct FanBoostActivatedEvent : IEvent
{
}

public struct FanBoostRemovedCardsEvent : IEvent
{
    public List<SessionID> CardIDs;
    public FanBoostRemovedCardsEvent(List<SessionID> cardIDs)
    {
        CardIDs = cardIDs;
    }
}

/// <summary>
/// Fan boost controller is not in charge of activating the boost, but to perform the missile boost logic
/// upon activation, correctly.
/// </summary>
public class FanBoostController : BaseController
{
    public FanBoostController()
    {
        Subscribe<FanBoostActivatedEvent>(ActivateFanBoost);
    }

    private void ActivateFanBoost(FanBoostActivatedEvent evt)
    {
        var topCards = Session.CurrentPuzzle.TopPuzzleCards;
        var topCardIDs = topCards.ConvertAll(c => c.ID);
        
        Debug.Log($"FanBoostController - Removing cards: {string.Join(", ", topCards)}");
        
        Session.CurrentPuzzle.RemoveCardsFromPuzzle(topCardIDs);
        
        Notify(new CardsRemovedEvent(topCardIDs));
        Notify(new FanBoostRemovedCardsEvent(topCardIDs));
    }
}
