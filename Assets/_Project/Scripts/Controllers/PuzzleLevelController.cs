using UnityEngine;

public struct NextLevelClickedEvent : IEvent
{
}

public struct PreviousLevelClickedEvent : IEvent
{
}

public struct PuzzleLevelChangedEvent : IEvent
{
}

public class PuzzleLevelController : BaseController
{
    public PuzzleLevelController()
    {
        Subscribe<NextLevelClickedEvent>(NextLevelClicked);
        Subscribe<PreviousLevelClickedEvent>(PreviousLevelClicked);
    }

    private void PreviousLevelClicked(PreviousLevelClickedEvent evt)
    {
        if(Session.SelectedLevel == 0)
            return;
        
        Session.SelectedLevel--;
        Game.EventHub.Notify(new PuzzleLevelChangedEvent());
    }

    private void NextLevelClicked(NextLevelClickedEvent evt)
    {
        //  TODO: Replace by actual level count
        if(Session.SelectedLevel == Consts.PRELOADED_PUZZLE_IDS.Count - 1)
            return;
        
        Session.SelectedLevel++;
        Game.EventHub.Notify(new PuzzleLevelChangedEvent());
    }
}
