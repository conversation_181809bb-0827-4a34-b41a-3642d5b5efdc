public struct WildCardBoostActivatedEvent : IEvent
{
}

/// <summary>
/// Wild card boost controller is not in charge of activating the boost, but to perform the wild card boost logic
/// upon activation, correctly.
/// </summary>
public class WildCardBoostController : BaseController
{
    public WildCardBoostController()
    {
        Subscribe<WildCardBoostActivatedEvent>(ActivateWildCardBoost);
    }

    private void ActivateWildCardBoost(WildCardBoostActivatedEvent evt)
    {
        Session.WildCard.UsesLeft++;
        Game.EventHub.Notify(new WildCardAmountChangedEvent());
    }
}
