
public struct WildCardPlayedEvent : IEvent
{
    public CardData CardData;

    public WildCardPlayedEvent(CardData cardData)
    {
        CardData = cardData;
    }
}

public struct WildCardAmountChangedEvent : IEvent
{
}

public struct WildCardPurchasedEvent : IEvent
{
}

public class WildCardController : BaseController
{
    public WildCardController()
    {
        Subscribe<WildCardButtonClickedEvent>(ActivateWildCard);
        Subscribe<WildCardPurchasedEvent>(AddWildCard);
    }

    private void AddWildCard(WildCardPurchasedEvent evt)
    {
        Session.WildCard.UsesLeft += Consts.WILD_CARD_PURCHASED_USES;
        Game.EventHub.Notify(new WildCardAmountChangedEvent());
    }

    private void ActivateWildCard(WildCardButtonClickedEvent evt)
    {
        if (Session.WildCard.UsesLeft == 0)
        {
            RefillWildCards();
        }
        
        Session.WildCard.UsesLeft--;
        Game.EventHub.Notify(new WildCardAmountChangedEvent());

        var wildCardData = new CardData(Consts.WILD_CARD_STRING);
        Session.CurrentPuzzle.SetTopPileCard(wildCardData);
        
        Game.EventHub.Notify(new WildCardPlayedEvent(wildCardData));
    }

    private void RefillWildCards()
    {
        Session.WildCard.UsesLeft = Consts.WILD_CARD_PURCHASED_USES;
        Game.EventHub.Notify(new WildCardAmountChangedEvent());
    }
}
