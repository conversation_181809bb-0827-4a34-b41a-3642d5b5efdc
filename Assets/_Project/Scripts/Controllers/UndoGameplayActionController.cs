using System.Collections.Generic;
using UnityEngine;

public class UndoGameplayActionController : BaseController
{
    public UndoGameplayActionController()
    {
        SubscribeToPlayerActions();
        SubscribeToCascadingEvents();
    }

    private void SubscribeToCascadingEvents()
    {
        //  TODO: No need to store every single action, only the summary of changes
        Subscribe<StreakIncreasedEvent>(evt => StoreEvent(evt));
        Subscribe<CardsRemovedEvent>(evt => StoreEvent(evt));
        //Subscribe<FanBoostRemovedCardsEvent>(StoreFanBoostEvent);
        //Subscribe<MissileBoostActivatedEvent>(StoreMissileBoostEvent);
        Subscribe<WildCardBoostActivatedEvent>(evt => StoreEvent(evt));
    }

    private void SubscribeToPlayerActions()
    {
        Subscribe<DeckCardPlayedEvent>(evt => StartNewActionChain(evt));
        Subscribe<PuzzleCardPlayedEvent>(evt => StartNewActionChain(evt));
    }

    private void StartNewActionChain(IEvent playerActionEvent)
    {
        Game.DataManager.Session.UndoAction.CurrentActionChain = new UndoActionChainData
        {
            PlayerInteractionEvent = playerActionEvent,
            CascadingEvents = new Stack<IEvent>()
        };
    }
    
    private void StoreEvent (IEvent evt)
    {
        Game.DataManager.Session.UndoAction.CurrentActionChain.CascadingEvents.Push(evt);
    }
    
    private void ActivateUndo()
    {
        //  Iterate over data
        //  Allow view to control visual sequence based on the undo data
        //  Clear undo data
        //  Allow player to perform actions again
    }
}
