using System.Collections.Generic;
using UniRx;
using UnityEngine;

public struct CardsRemovedEvent : IEvent
{
    public List<SessionID> Cards;
    public CardsRemovedEvent(List<SessionID> cards)
    {
        Cards = cards;
    }
}

public struct PuzzleCardPlayedEvent : IEvent
{
    public SessionID CardID;

    public PuzzleCardPlayedEvent(SessionID cardID)
    {
        CardID = cardID;
    }
}

public struct DeckCardPlayedEvent : IEvent
{
    public SessionID CardID;

    public DeckCardPlayedEvent(SessionID cardID)
    {
        CardID = cardID;
    }
}

public class CardInteractionsController : BaseController
{
    public CardInteractionsController()
    {
        Subscribe<PuzzleCardClickedEvent>(OnCardClicked);
        Subscribe<DeckClickedEvent>(OnDeckClicked);
        Subscribe<CardsRemovedEvent>(_ => RecalculateCardElements());
        Subscribe<CardPileStateChangedEvent>(_ => RecalculateCardElements());
    }

    private void RecalculateCardElements()
    {
        RecalculatePlayableCards();
    }

    private void OnDeckClicked(DeckClickedEvent evt)
    {
        if (Session.CurrentPuzzle.DeckSize == 0)
        {
            Debug.LogError("CardInteractionController - Deck is empty, and yet, it was clicked");
            return;
        }    
        
        var deckCard = Session.CurrentPuzzle.MoveDeckCardToPlayedPile();
        
        RecalculatePlayableCards();

        Game.EventHub.Notify(new DeckCardPlayedEvent(deckCard.ID));
    }

    private void OnCardClicked(PuzzleCardClickedEvent evt)
    {
        if(!IsPlayable(evt.CardID)) return;
        
        var card = Session.CurrentPuzzle.GetCardByID(evt.CardID);
        var pileCard = Session.CurrentPuzzle.GetTopPlayedPileCard();

        if (IsCardPileCompatible(pileCard, card))
        {
            Session.CurrentPuzzle.MoveCardToPlayedPile(evt.CardID);
            RecalculatePlayableCards();
            Game.EventHub.Notify(new PuzzleCardPlayedEvent(evt.CardID));
        }
    }
    
    private void RecalculatePlayableCards()
    {
        Session.CurrentPuzzle.PlayablePuzzleCards.Clear();
        var pileCard = Session.CurrentPuzzle.GetTopPlayedPileCard();
        foreach (var card in Session.CurrentPuzzle.TopPuzzleCards)
        {
            if (IsCardPileCompatible(pileCard, card))
            {
                Session.CurrentPuzzle.PlayablePuzzleCards.Add(card);
            }
        }
    }

    private static bool IsCardPileCompatible(CardData pileCard, CardData card)
    {
        if(card.Suit == CardSuit.Joker || pileCard?.Suit == CardSuit.Joker) return true;
        
        return pileCard == null ||
               card.Rank == (pileCard.Rank + 1) ||
               card.Rank == (pileCard.Rank - 1) ||
               card.Rank == CardRank.Two && pileCard.Rank == CardRank.Ace ||
               card.Rank == CardRank.Ace && pileCard.Rank == CardRank.Two;
    }

    public bool IsPlayable(SessionID sessionID)
    {
        var card = Session.CurrentPuzzle.GetCardByID(sessionID);
        
        if (card == null)
        {
            Debug.LogError($"Card {sessionID.ID} not found in puzzle");
            return false;
        }
        
        return card.BlockedByCards.Count == 0;
    }
}
