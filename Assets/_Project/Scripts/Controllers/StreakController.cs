using UnityEngine;

public struct StreakLevelCompleteEvent : IEvent
{
    public bool IsRepeat;

    public StreakLevelCompleteEvent(bool isRepeat)
    {
        IsRepeat = isRepeat;
    }
}

public struct StreakResetEvent : IEvent
{
}

public struct StreakIncreasedEvent : IEvent
{
}

public class StreakController : BaseController 
{
    public StreakController()
    {
        Subscribe<DeckCardPlayedEvent>(OnDeckCardPlayed);
        Subscribe<PuzzleCardPlayedEvent>(OnPuzzleCardPlayed);
        Subscribe<PuzzleStartEvent>(InitStreakData);
    }

    private void InitStreakData(PuzzleStartEvent evt)
    {
        Session.Streak = new StreakData();
    }

    private void OnPuzzleCardPlayed(PuzzleCardPlayedEvent evt)
    {
        IncreaseStreak();
    }

    private void OnDeckCardPlayed(DeckCardPlayedEvent evt)
    {
        ResetStreak();
    }

    private void ResetStreak()
    {
        Session.Streak.CurrentStreak = 0;
        Game.EventHub.Notify(new StreakResetEvent());
    }
    private void IncreaseStreak()
    {
        var currentStreak = ++Session.Streak.CurrentStreak;
        var streakCap = Consts.STREAK_LEVELS[Session.Streak.LastUnlockedStreakLevel];
        Game.EventHub.Notify(new StreakIncreasedEvent());

        if (currentStreak >= streakCap)
        {
            UnlockNextStreakLevel();
        }
    }

    private void UnlockNextStreakLevel()
    {
        //  Last level only resets the streak count
        if(Session.Streak.LastUnlockedStreakLevel >= Consts.STREAK_LEVELS.Length - 1)
        {
            Session.Streak.CurrentStreak = 0;
            Game.EventHub.Notify(new StreakLevelCompleteEvent(true));
        }
        else
        {
            Game.EventHub.Notify(new StreakLevelCompleteEvent(false));
            Session.Streak.LastUnlockedStreakLevel++;
        }
        
    }
}
