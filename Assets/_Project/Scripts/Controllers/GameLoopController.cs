using UnityEngine;
using UnityEngine.SceneManagement;

public struct PuzzleEndEvent : IEvent
{
}

public struct PuzzleStartEvent : IEvent
{
    public int PuzzleId;

    public PuzzleStartEvent(int puzzleId)
    {
        PuzzleId = puzzleId;
    }
}

public struct PuzzleLoadedEvent : IEvent
{
}

public class GameLoopController : BaseController
{
    public GameLoopController()
    {
        AddSubscriptions();
    }

    private void AddSubscriptions()
    {
        Subscribe<PuzzleCardClickedEvent>(CheckForGameEnd);
        Subscribe<DeckCardPlayedEvent>(CheckForGameEnd);
        Subscribe<PuzzleStartEvent>(LoadPuzzleData);
        Subscribe<LoadingProgressedEvent>(LoadGameScene);
    }

    private void LoadGameScene(LoadingProgressedEvent evt)
    {
        if(evt.completedStep != AvailabilityProgress.Complete)
            return;
        
        SceneManager.LoadScene(Consts.GAME_SCENE_NAME);
        
        //  TODO: View should handle this
        Game.GameplayCamera = UnityEngine.Camera.main;
    }

    private void LoadPuzzleData(PuzzleStartEvent evt)
    {
        //  TODO: Replace with proper configuration per level
        Game.DataManager.Session.CurrentPuzzle = new PuzzleData(Game.ConfigManager.LevelsConfig.Levels[Game.DataManager.Session.SelectedLevel]);
    }

    private void CheckForGameEnd(PuzzleCardClickedEvent evt)
    {
        if(Game.DataManager.Session.CurrentPuzzle.PuzzleCardsLeft == 0)
        {
            Game.EventHub.Notify(new PuzzleEndEvent());
        }

        CheckIfPossibleMovesLeft();
    }

    private void CheckForGameEnd(DeckCardPlayedEvent evt)
    {
        CheckIfPossibleMovesLeft();
    }
    
    private void CheckIfPossibleMovesLeft()
    {
        // Debug.Log($"GameLoopController - Checking for game end. " +
        //           $"Playable cards: {Session.CurrentPuzzle.PlayablePuzzleCards.Count}" +
        //           $"Deck size: {Session.CurrentPuzzle.DeckSize}");
        
        if(Session.CurrentPuzzle.DeckSize == 0 &&
           Session.CurrentPuzzle.PlayablePuzzleCards.Count == 0 && 
           !AreBoostersAvailable())
        {
            Game.EventHub.Notify(new PuzzleEndEvent());
        }
    }

    private bool AreBoostersAvailable()
    {
        // TODO: Add possible boosts check here
        return false;
    }
}
