using System.Collections.Generic;
using UnityEngine;

public struct MissileBoostActivatedEvent : IEvent
{
}

public struct MissileBoostRemovedCardEvent : IEvent
{
    public SessionID CardID;
    public MissileBoostRemovedCardEvent(SessionID cardID)
    {
        CardID = cardID;
    }
}

/// <summary>
/// Missile boost controller is not in charge of activating the boost, but to perform the missile boost logic
/// upon activation, correctly.
/// </summary>
public class MissileBoostController : BaseController
{
    public MissileBoostController()
    {
        Subscribe<MissileBoostActivatedEvent>(ActivateMissileBoost);
    }

    private void ActivateMissileBoost(MissileBoostActivatedEvent evt)
    {
        var randomIndex = Random.Range(0, Session.CurrentPuzzle.TopPuzzleCards.Count);
        var randomTopCard = Session.CurrentPuzzle.TopPuzzleCards[randomIndex];
        
        Debug.Log($"MissileBoostController - Removing card {randomTopCard.ID}-{randomTopCard.Rank} with sessionID: {randomTopCard.ID.ID}");
        Session.CurrentPuzzle.RemoveCardFromPuzzle(randomTopCard.ID);
        
        Notify(new CardsRemovedEvent(new List<SessionID> {randomTopCard.ID}));
        Notify(new MissileBoostRemovedCardEvent(randomTopCard.ID));
    }
}
