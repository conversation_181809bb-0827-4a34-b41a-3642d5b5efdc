public class StreakBoostersController : BaseController
{
    public StreakBoostersController()
    {
        Subscribe<StreakLevelCompleteEvent>(ActivateBooster);
    }

    private void ActivateBooster(StreakLevelCompleteEvent evt)
    {
        //  TODO: There should be a data for which level triggers which booster
        switch (Session.Streak.LastUnlockedStreakLevel)
        {
            case 0:
                Game.EventHub.Notify(new MissileBoostActivatedEvent());
                break;
            case 1:
                Game.EventHub.Notify(new FanBoostActivatedEvent());
                break;
            case 2:
                Game.EventHub.Notify(new WildCardBoostActivatedEvent());
                break;
        }
    }
}
