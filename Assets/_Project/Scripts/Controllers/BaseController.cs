using System;
using System.Collections.Generic;

public abstract class BaseController : IDisposable
{
    protected List<IDisposable> _subscriptions = new();

    protected SessionDataModel Session => Game.DataManager.Session;
    protected IDisposable Subscribe<T>(Action<T> handler) where T : struct, IEvent
    {
        return Game.EventHub.Subscribe(handler);
    }
    
    protected void Notify<T>(T e) where T : struct, IEvent
    {
        Game.EventHub.Notify(e);
    }

    protected void UnSubscribe()
    {
        _subscriptions.ForEach(s => s.Dispose());
    }

    public void Dispose()
    {
        UnSubscribe();
    }
}
