using System;
using System.Collections.Generic;

public class Consts
{
    //  Build settings
    public const string BUILD_INFO_PATH = "Assets/_Project/BuildInfo.json";
    
    //  Editor settings
    public const string LOADING_SCENE_PATH = "Assets/_Project/Scenes/Loading.unity";
    
    //  Input
    public const float RAYCAST_DISTANCE_TO_CARDS = 500f;
    
    //  Gameplay
    public static string FEATURE_PREFABS_GROUP_NAME = "FeaturePrefabs";
    public const string CARD_SPRITES_GROUP_NAME = "BaseCardImages";
    public const string GAME_SCENE_NAME = "GameDemoScene";
    public const string PUZZLE_PREFABS_GROUP_NAME = "PuzzlePrefabs";
    public const string PUZZLE_ASSET_PREFIX = "puzzle_";
    public const string DEFAULT_CARD_ASSET_NAME = "BaseCard";
    public static List<int> PRELOADED_PUZZLE_IDS = new List<int>(){ 1 };
    
    //  Streak
    public static string STREAK_BAR_PREFIX = "streakprogressbar_";
    public static int[] STREAK_LEVELS = new int[3] { 3, 5, 7 };
    
    //  WildCard
    public const int WILD_CARD_USES_PER_PUZZLE = 2;
    public const int WILD_CARD_PURCHASED_USES = 1;
    public const string WILD_CARD_STRING = "j2";
    
    //  Deck refill
    public const int DECK_REFILL_CARD_AMOUNT = 5;
    public const int DECK_REFILL_PLAYABLE_CARDS = 1;
    
    //  Buttons
    public const string BUTTON_PUZZLE_START_NAME = "start_puzzle_level";
    public const string BUTTON_PUZZLE_END_NAME = "puzzletolobby";
    public const string BUTTON_PUZZLE_END_CLOSE_POPUP = "puzzleendclosepopup";
    public const string BUTTON_DECK_REFILL_SHOW_OFFER = "deck_refill_show_offer";


    //  TODO: Replace by actual puzzle filling logic
    public static List<CardConfig> DEBUGGenerateTestPuzzle()
    {
        var cm = new Dictionary<int, CardConfig>();

        cm.Add(1, new CardConfig(1, "s14", new int[] {}, new int[] { 2 }));
        cm.Add(2, new CardConfig(2, "s10", new int[] { 1 }, new int[] { 3 }));
        cm.Add(3, new CardConfig(3, "s6", new int[] { 2, 4, 6, 8 }, new int[] {}));
        cm.Add(4, new CardConfig(4, "h9", new int[] { 5 }, new int[] { 3 }));
        cm.Add(5, new CardConfig(5, "h13", new int[] {}, new int[] { 4 }));
        cm.Add(6, new CardConfig(6, "c8", new int[] { 7 }, new int[] { 3 }));
        cm.Add(7, new CardConfig(7, "c12", new int[] {}, new int[] { 6 }));
        cm.Add(8, new CardConfig(8, "d7", new int[] { 9 }, new int[] { 3 }));
        cm.Add(9, new CardConfig(9, "d11", new int[] {}, new int[] { 8 }));

        return new List<CardConfig>(cm.Values);
    }

    public static List<CardConfig> DEBUGGenerateLevel1Puzzle()
    {
        var cm = new Dictionary<int, CardConfig>();
        
        cm.Add(1, new CardConfig(1, "d2", new int[] {}, new int[] { 2 }));
        cm.Add(2, new CardConfig(2, "d13", new int[] { 1 }, new int[] { 3 }));
        cm.Add(3, new CardConfig(3, "s7", new int[] { 2, 4, 6, 8 }, new int[] {}));
        cm.Add(4, new CardConfig(4, "h14", new int[] { 5 }, new int[] { 3 }));
        
        cm.Add(5, new CardConfig(5, "c2", new int[] {}, new int[] { 4 }));
        cm.Add(6, new CardConfig(6, "c7", new int[] { 7 }, new int[] { 3 }));
        cm.Add(7, new CardConfig(7, "h10", new int[] {}, new int[] { 6 }));
        cm.Add(8, new CardConfig(8, "d3", new int[] { 9 }, new int[] { 3 }));
        
        cm.Add(9, new CardConfig(9, "c10", new int[] {}, new int[] { 8 }));
        cm.Add(10, new CardConfig(10, "s5", new int[] {}, new int[] {}));
        cm.Add(11, new CardConfig(11, "h13", new int[] {}, new int[] {}));
        cm.Add(12, new CardConfig(12, "c14", new int[] {}, new int[] {}));
        
        cm.Add(13, new CardConfig(13, "s14", new int[] {}, new int[] { 14 }));
        cm.Add(14, new CardConfig(14, "s6", new int[] { 13 }, new int[] { 15 }));
        cm.Add(15, new CardConfig(15, "h8", new int[] { 14 }, new int[] { 16 }));
        cm.Add(16, new CardConfig(16, "h12", new int[] { 15 }, new int[] {}));
        
        cm.Add(17, new CardConfig(17, "c8", new int[] {}, new int[] { 19 }));
        cm.Add(18, new CardConfig(18, "h5", new int[] {}, new int[] { 19 }));
        cm.Add(19, new CardConfig(19, "s8", new int[] { 17, 18 }, new int[] {}));
        
        cm.Add(20, new CardConfig(20, "c3", new int[] {}, new int[] { 22 }));
        cm.Add(21, new CardConfig(21, "d4", new int[] {}, new int[] { 22 }));
        cm.Add(22, new CardConfig(22, "s11", new int[] { 20, 21 }, new int[] {}));
        
        cm.Add(23, new CardConfig(23, "s13", new int[] {}, new int[] { 25 }));
        cm.Add(24, new CardConfig(24, "d6", new int[] {}, new int[] { 25 }));
        cm.Add(25, new CardConfig(25, "d9", new int[] { 23, 24 }, new int[] {}));

        return new List<CardConfig>(cm.Values);
    }

    public static Stack<CardConfig> DEBUGGenerateLevel1Deck()
    {
        var deck = new Stack<CardConfig>();
        
        deck.Push(new CardConfig(0, "c12", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "s10", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "s7", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "d8", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "c11", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "s3", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "d7", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "d14", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "h7", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "h6", new int[] {}, new int[] {}));
        deck.Push(new CardConfig(0, "h2", new int[] {}, new int[] {}));

        return deck;
    }
}
