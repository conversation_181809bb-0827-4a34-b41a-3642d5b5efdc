using UnityEngine;
using UnityEngine.AddressableAssets;
using UnityEngine.ResourceManagement.AsyncOperations;
using UnityEngine.ResourceManagement.ResourceLocations;
using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;

public class AssetManager : BaseManager 
{
    private Dictionary<string, Sprite> spriteCache = new Dictionary<string, Sprite>();
    private Dictionary<string, GameObject> prefabCache = new Dictionary<string, GameObject>();
    
    public AssetManager()
    {
    }
    
    public override async UniTask Initialize()
    {
        //  TODO: Perhaps there's a better way to list all preloaded elements
        await PreloadSpritesFromGroup(Consts.CARD_SPRITES_GROUP_NAME);
        
        var prefabNames = Consts.PRELOADED_PUZZLE_IDS.Select(id => $"{Consts.PUZZLE_ASSET_PREFIX}{id}").ToList();
        await PreloadPuzzlePrefabs(Consts.PUZZLE_PREFABS_GROUP_NAME, prefabNames);
    }
    
    public async UniTask<Sprite> GetSpriteByName(string name)
    {
        if (spriteCache.TryGetValue(name, out Sprite cachedSprite))
        {
            return cachedSprite;
        }

        var asyncOperation = Addressables.LoadAssetAsync<Sprite>(name);
        await asyncOperation.Task;

        if (asyncOperation.Status == AsyncOperationStatus.Succeeded)
        {
            Sprite loadedSprite = asyncOperation.Result;
            spriteCache[name] = loadedSprite;
            return loadedSprite;
        }
        
        Debug.LogError($"AssetManager - Failed to load sprite: {name}");
        return null;
    }

    public async UniTask PreloadSpritesFromGroup(string groupName)
    {
        // Get all resource locations from the specified group
        var locationsOperation = Addressables.LoadResourceLocationsAsync(groupName, typeof(Sprite));
        await locationsOperation.Task;

        if (locationsOperation.Status != AsyncOperationStatus.Succeeded)
        {
            Debug.LogError($"AssetManager - Failed to load resource locations for group: {groupName}");
            return;
        }

        IList<IResourceLocation> locations = locationsOperation.Result;
        
        // Load all sprites from the locations
        foreach (var location in locations)
        {
            var asyncOperation = Addressables.LoadAssetAsync<Sprite>(location);
            await asyncOperation.Task;

            if (asyncOperation.Status == AsyncOperationStatus.Succeeded)
            {
                Sprite loadedSprite = asyncOperation.Result;
                spriteCache[location.PrimaryKey] = loadedSprite;
            }
            else
            {
                Debug.LogError($"AssetManager - Failed to preload sprite: {location.PrimaryKey} from group: {groupName}");
            }
        }
    }

    public void ClearSpriteCache()
    {
        foreach (var sprite in spriteCache.Values)
        {
            Addressables.Release(sprite);
        }
        spriteCache.Clear();
    }


    private async UniTask<GameObject> GetPrefabByName(string prefabName)
    {
        if (prefabCache.TryGetValue(prefabName, out GameObject cachedPrefab))
        {
            return cachedPrefab;
        }

        var asyncOperation = Addressables.LoadAssetAsync<GameObject>(prefabName);
        await asyncOperation.Task;

        if (asyncOperation.Status == AsyncOperationStatus.Succeeded)
        {
            GameObject loadedPrefab = asyncOperation.Result;
            prefabCache[prefabName] = loadedPrefab;
            return loadedPrefab;
        }

        Debug.LogError($"AssetManager - Failed to load prefab: {prefabName}");
        return null;
    }

    public async UniTask<GameObject> GetPuzzlePrefabByName(int puzzleId)
    {
        string prefabName = $"{Consts.PUZZLE_ASSET_PREFIX}{puzzleId}";
        return await GetPrefabByName(prefabName);
    }
    
    public async UniTask<GameObject> GetCardPrefabByName(CardData card)
    {
        //  Currently we only have one prefab type of card
        string prefabName = $"{Consts.DEFAULT_CARD_ASSET_NAME}";
        return await GetPrefabByName(prefabName);
    }

    public void ClearPrefabCache()
    {
        foreach (var prefab in prefabCache.Values)
        {
            Addressables.Release(prefab);
        }
        prefabCache.Clear();
    }

    public async UniTask PreloadPrefabsFromGroup(string groupName, List<string> prefabNames)
    {
        // Get all resource locations from the specified group
        var locationsOperation = Addressables.LoadResourceLocationsAsync(groupName, typeof(GameObject));
        await locationsOperation.Task;

        if (locationsOperation.Status != AsyncOperationStatus.Succeeded)
        {
            Debug.LogError($"AssetManager - Failed to load resource locations for group: {groupName}");
            return;
        }

        IList<IResourceLocation> locations = locationsOperation.Result;
        
        // Filter locations based on prefabNames
        var filteredLocations = locations.Where(loc => prefabNames.Contains(loc.PrimaryKey)).ToList();
        
        // Load all prefabs from the filtered locations
        foreach (var location in filteredLocations)
        {
            var asyncOperation = Addressables.LoadAssetAsync<GameObject>(location);
            await asyncOperation.Task;

            if (asyncOperation.Status == AsyncOperationStatus.Succeeded)
            {
                GameObject loadedPrefab = asyncOperation.Result;
                prefabCache[location.PrimaryKey] = loadedPrefab;
            }
            else
            {
                Debug.LogError($"AssetManager - Failed to preload prefab: {location.PrimaryKey} from group: {groupName}");
            }
        }
    }

    public async UniTask PreloadPuzzlePrefabs(string groupName, List<string> prefabNames)
    {
        await PreloadPrefabsFromGroup(groupName, prefabNames);
    }

    public async UniTask<GameObject> GetFeaturePrefabByName(string prefabName)
    {
        return await GetPrefabByName(prefabName);
    }
}
