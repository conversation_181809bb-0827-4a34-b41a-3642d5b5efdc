using System;
using System.IO;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class DataManager : BaseManager
{
    public PlayerDataModel Save { get; private set; }
    public SessionDataModel Session { get; } = new();
    
    public override async UniTask Initialize()
    {
        Save = await LoadFromFileAsync<PlayerDataModel>();
    }
    
    private bool SaveToFile()
    {
        try
        {
            var path = FullPath<PlayerDataModel>();
            var json = JsonUtility.ToJson(Save, true);
            File.WriteAllText(path, json);
            return true;
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("DataManager - Failed to write file. Error: {0}", e);
            return false;
        }
    }
    
    private string FullPath<T>(string fileType = ".json")
    {
        var fileName = $"{typeof(T)}{fileType}";
        return Path.Combine(Application.persistentDataPath, fileName);
    }
    
    private async UniTask<T> LoadFromFileAsync<T>() where T : new()
    {
        T data = default;
        try
        {
            var path = FullPath<T>();

            if (!File.Exists(path))
            {
                return new T();
            }
            
            string json = await File.ReadAllTextAsync(path).AsUniTask(); 
            data = JsonUtility.FromJson<T>(json);
        }
        catch (Exception e)
        {
            Debug.LogErrorFormat("DataManager - Loading player data failed. Error: {0}", e);
        }

        return data ?? new T();
    }
}
