using System.Collections.Generic;
using System.Threading.Tasks;
using Cysharp.Threading.Tasks;
using UnityEngine;

public class ConfigManager : BaseManager
{
    public LevelsConfig LevelsConfig;
    
    public override async UniTask Initialize()
    {
        LevelsConfig = await LoadLevelsConfig();
    }

    private async Task<LevelsConfig> LoadLevelsConfig()
    {
        //  TODO: Added insignificant delay for testing. Replace by actual remote loading of config.
        await UniTask.Delay(100);
        var levels = new LevelsConfig();
        levels.GenerateTestingData();
        return levels;
    }
}
