public class CoreFeature : BaseFeature
{
    public CoreFeature()
    {
        AddController<CardInteractionsController>();
        AddController<GameLoopController>();
        AddController<PuzzleLevelController>();
        
        AddController<StreakController>();
        AddController<WildCardController>();
        AddController<DeckRefillController>();
        
        //  Boosters
        AddController<MissileBoostController>();
        AddController<StreakBoostersController>();
        AddController<FanBoostController>();
        AddController<WildCardBoostController>();
        
        //  Undo
        AddController<UndoGameplayActionController>();
    }
}
