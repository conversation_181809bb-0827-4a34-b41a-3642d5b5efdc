using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class BaseFeature
{
    protected readonly List<object> Controllers = new List<object>();

    protected void AddController<T>() where T : BaseController, new()
    {
        var type = typeof(T);

        if (Controllers.FirstOrDefault(c => c.GetType() == type) == null)
        {
            try
            {
                var controller = Activator.CreateInstance(typeof(T));
                Controllers.Add(controller);
            }
            catch (Exception e)
            {
                Debug.LogError($"BaseFeature - Failed to add controller. Error: {e}");
            }
        }
    }

    protected void RemoveController<T>() where T : BaseController
    {
        var type = typeof(T);
        var index = Controllers.FindIndex(c => c.GetType() == type);

        if (index >= 0)
        {
            Controllers.RemoveAt(index);
        }
    }
}
