using System.Collections.Generic;
public class CardData
{
    public SessionID ID;
    public int VisualCardIndex;
    
    public CardSuit Suit;
    public CardRank Rank;

    public List<int> BlockingOtherCards;
    public List<int> BlockedByCards;

    public CardData(CardConfig cardConfig)
    {
        ID = new SessionID();
        VisualCardIndex = cardConfig.VisualPositionID;
        Suit = cardConfig.Suit;
        Rank = cardConfig.Rank;
        BlockingOtherCards = new List<int>(cardConfig.BlockingOtherCards);
        BlockedByCards = new List<int>(cardConfig.BlockedByCards);
    }
    
    public CardData(string cardString)
    {
        ID = new SessionID();

        if (cardString.Contains('-'))
        {
            ParsePlayableCard(cardString);
        }
        else
        {
            ParseGenericCard(cardString);   
        }
    }

    private void ParseGenericCard(string cardString)
    {
        // First character is the suit
        char suitChar = cardString[0];
        Suit = (CardSuit)suitChar;
        
        // Remaining characters are the rank
        string rankPart = cardString.Substring(1);
        Rank = (CardRank)int.Parse(rankPart);
    }

    private void ParsePlayableCard(string cardString)
    {
        string[] parts = cardString.Split('-');
        
        VisualCardIndex = int.Parse(parts[0]);
        string suitRankPart = parts[1];
        
        // First character is the suit
        char suitChar = suitRankPart[0];
        Suit = (CardSuit)suitChar;
        
        // Remaining characters are the rank
        string rankPart = suitRankPart.Substring(1);
        Rank = (CardRank)int.Parse(rankPart);
    }
    
    public override string ToString()
    {
        return $"[{ID.ID}][{VisualCardIndex}]-{Suit}{Rank}";
    }
}
