using System;
using System.Threading;

public class SessionID : IComparable
{
    private static int _sequencedIDs;
    private int _myID;

    public int ID
    {
        get
        {
            if (_myID != 0) 
                return _myID;
            
            Interlocked.Increment(ref _sequencedIDs);
            _myID = _sequencedIDs;

            return _myID;
        }
    }

    public int CompareTo(object obj)
    {
        return ID.CompareTo(((SessionID)obj).ID);
    }
}

public class SessionDataModel
{
    public PuzzleData CurrentPuzzle;
    public SceneObjectReferences SceneObjects = new();
    public StreakData Streak = new();
    public WildCardData WildCard = new();
    public UndoActionData UndoAction = new();
    public int SelectedLevel = 0;
}
