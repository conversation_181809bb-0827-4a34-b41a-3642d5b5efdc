using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public struct CardPileStateChangedEvent : IEvent
{
}

public struct DeckStateChangedEvent : IEvent
{
}

public class PuzzleData
{
    //  TODO: Convert to config
    private Dictionary<int, Transform> _cardPositions = new Dictionary<int, Transform>();
    
    private Dictionary<SessionID, CardData> _puzzleCards = new Dictionary<SessionID, CardData>();
    private List<CardData> _topPuzzleCards = new List<CardData>();
    private Stack<CardData> _playedCardsPile = new Stack<CardData>();
    private Stack<CardData> _deck = new Stack<CardData>();
    
    
    public List<CardData> PlayablePuzzleCards { get; } = new List<CardData>();
    public List<CardData> TopPuzzleCards => _topPuzzleCards;
    public int PuzzleCardsLeft => _puzzleCards.Count;
    public int DeckSize => _deck.Count;
    
    public Dictionary<int, Transform> CardPositions => _cardPositions;
    
    public PuzzleData(LevelConfig levelConfig)
    {
        PopulatePuzzle(levelConfig.Cards, levelConfig.Deck);
    }
    
    public void PopulatePuzzle(List<CardConfig> cards, Stack<CardConfig> deck)
    {
        foreach (var card in cards)
        {
            CardData cardData = new CardData(card);
            
            if(cardData.BlockedByCards.Count == 0)
            {
                _topPuzzleCards.Add(cardData);
            }
            
            _puzzleCards.Add(cardData.ID, cardData);
        }
        
        foreach (var card in deck)
        {
            CardData cardData = new CardData(card);
            _deck.Push(cardData);
        }
    }
    
    public CardData GetCardByVisualPosition(int visualPositionID)
    {
        return _puzzleCards.Values.FirstOrDefault(c => c.VisualCardIndex == visualPositionID);
    }
    
    public CardData GetCardByID(SessionID sessionID)
    {
        _puzzleCards.TryGetValue(sessionID, out CardData card);
        return card;
    }

    public CardData GetTopPlayedPileCard()
    {
        return _playedCardsPile.Count == 0 ? null : _playedCardsPile.Peek();
    }
    
    public void MoveCardToPlayedPile(SessionID sessionID)
    {
        _puzzleCards.TryGetValue(sessionID, out CardData removedCard);
        if (removedCard == null)
        {
            Debug.LogError($"Card {sessionID.ID} could not be removed from puzzle");
            return;
        }
        
        _playedCardsPile.Push(removedCard);
        Game.EventHub.Notify(new CardPileStateChangedEvent());
        
        RemoveCardFromPuzzle(sessionID);
    }

    public void RemoveCardFromPuzzle(SessionID sessionID)
    {
        _puzzleCards.TryGetValue(sessionID, out CardData removedCard);
        if (removedCard == null)
        {
            Debug.LogError($"Card {sessionID.ID} could not be removed from puzzle");
            return;
        }
        
        foreach (var visualPositionID in removedCard.BlockingOtherCards)
        {
            var card = GetCardByVisualPosition(visualPositionID);
            if (card != null)
            {
                card.BlockedByCards.Remove(removedCard.VisualCardIndex);
                if(card.BlockedByCards.Count == 0)
                {
                    _topPuzzleCards.Add(card);
                }
            }
        }
        
        _puzzleCards.Remove(sessionID);
        _topPuzzleCards.Remove(removedCard);
    }
    
    public void RecreateCardsInPuzzle(List<int> cardPositionIDs)
    {
        var currentLevel = Game.DataManager.Session.SelectedLevel;
        var puzzleConfig = Game.ConfigManager.LevelsConfig.Levels[currentLevel];
        
        foreach (var cardPositionID in cardPositionIDs)
        {
            var card = puzzleConfig.Cards.FirstOrDefault(c => c.VisualPositionID == cardPositionID);
            if (card.VisualPositionID != cardPositionID)
            {
                Debug.LogError($"Card {cardPositionID} not found in puzzle");
                continue;
            }

            CreateCard(card);
        }
    }

    private void CreateCard(CardConfig cardConfig)
    {
        var cardData = new CardData(cardConfig);
        _puzzleCards.Add(cardData.ID, cardData);
        
        if (cardData.BlockedByCards.Count == 0)
        {
            _topPuzzleCards.Add(cardData);
        }
        
        foreach (var visualPositionID in cardData.BlockingOtherCards)
        {
            var card = GetCardByVisualPosition(visualPositionID);
            if (card != null)
            {
                card.BlockedByCards.Add(cardData.VisualCardIndex);
            }
        }
    }

    public CardData MoveDeckCardToPlayedPile()
    {
        var card = _deck.Pop();
        Game.EventHub.Notify(new DeckStateChangedEvent());
        
        _playedCardsPile.Push(card); 
        Game.EventHub.Notify(new CardPileStateChangedEvent());
        return card;
    }
    
    public void SetTopPileCard(CardData card)
    {
        _playedCardsPile.Push(card);
        Game.EventHub.Notify(new CardPileStateChangedEvent());
    }


    public void RemoveCardsFromPuzzle(List<SessionID> removedCards)
    {
        foreach (var card in removedCards)
        {
            RemoveCardFromPuzzle(card);
        }
    }

    /// <summary>
    /// Cards are pushed, which means that the last card in the list will be the first to be drawn
    /// </summary>
    public void AddCardsToDeck(List<CardData> addedCards)
    {
        foreach (var card in addedCards)
        {
            _deck.Push(card);
        }
        
        Game.EventHub.Notify(new DeckStateChangedEvent());
    }
}
